# JupiterEd to Microsoft To-Do Chrome Extension

A powerful Chrome extension that automatically syncs your assignments from JupiterEd to Microsoft To-Do, helping you stay organized and never miss a deadline.

## ✨ Features

- **🔄 Automatic Sync**: Automatically detects and syncs assignments when you visit JupiterEd pages
- **📋 Smart Parsing**: Intelligently extracts assignment details including course, title, due dates, and descriptions
- **🎯 Customizable Lists**: Choose which Microsoft To-Do list to sync assignments to
- **🔐 Secure Authentication**: Uses Microsoft's official OAuth2 flow for secure access
- **📱 User-Friendly Interface**: Clean, modern popup and settings interface
- **🔔 Notifications**: Optional notifications to keep you informed of sync status
- **🛡️ Privacy-First**: No data stored on external servers - everything stays between you and Microsoft

## 🚀 Quick Start

### 1. Install the Extension
1. Download or clone this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right corner
4. Click "Load unpacked" and select the `JupiterEdMicrosoftToDo` folder

### 2. Set Up Microsoft Integration
1. Click the extension icon and select "Settings"
2. Follow the Azure App Registration setup guide
3. Enter your Azure App Client ID
4. Test authentication and select your preferred To-Do list

### 3. Start Syncing
1. Navigate to your JupiterEd assignments page
2. Click the extension icon and hit "Sync Assignments"
3. Or enable automatic sync in settings for hands-free operation

## 📖 Detailed Setup Guide

For complete setup instructions, see [SETUP.md](SETUP.md)

## 🎯 How It Works

1. **Detection**: The extension automatically detects when you're on a JupiterEd assignments page
2. **Extraction**: It scrapes assignment data including course names, titles, due dates, and descriptions
3. **Processing**: Dates are intelligently parsed and formatted for Microsoft To-Do
4. **Sync**: Assignments are securely uploaded to your chosen Microsoft To-Do list
5. **Feedback**: You receive notifications about the sync status and any issues

## 🔧 Configuration Options

### Authentication
- Azure App Client ID configuration
- Secure OAuth2 authentication with Microsoft
- Token management and refresh handling

### Sync Settings
- Choose target Microsoft To-Do list
- Enable/disable automatic sync
- Configure notification preferences

### Advanced Features
- Duplicate detection and prevention
- Custom date parsing for various formats
- Error handling and retry logic

## 🛠️ Technical Details

### Architecture
- **Manifest V3**: Uses the latest Chrome extension architecture
- **Content Script**: Handles page detection and data extraction
- **Background Script**: Manages authentication and API calls
- **Popup Interface**: Provides user controls and status information
- **Settings Page**: Comprehensive configuration interface

### Supported Platforms
- JupiterEd (primary target)
- Schoology (experimental support)
- Any page with compatible assignment table structure

### API Integration
- Microsoft Graph API for To-Do access
- Secure token storage using Chrome's identity API
- Rate limiting and error handling

## 🔒 Privacy & Security

- **No External Servers**: All data processing happens locally in your browser
- **Secure Authentication**: Uses Microsoft's official OAuth2 implementation
- **Minimal Permissions**: Only requests necessary permissions for functionality
- **Local Storage**: Settings and tokens stored securely in Chrome's storage
- **Open Source**: Full source code available for review

## 🐛 Troubleshooting

### Common Issues

**Authentication Problems**
- Verify your Azure App Client ID is correct
- Check that the redirect URI matches exactly
- Ensure Tasks.ReadWrite permission is granted

**Sync Issues**
- Make sure you're on the JupiterEd assignments page
- Check your internet connection
- Try re-authenticating in settings

**No Assignments Found**
- Verify the page has loaded completely
- Check that assignments are visible in the table
- Try refreshing the page

For more troubleshooting help, see [SETUP.md](SETUP.md) or [create an issue](https://github.com/Pikachoo1111/JupiterEdMicrosoftToDo/issues).

## 🤝 Contributing

Contributions are welcome! Please feel free to:

1. Report bugs or request features via [GitHub Issues](https://github.com/Pikachoo1111/JupiterEdMicrosoftToDo/issues)
2. Submit pull requests for improvements
3. Help improve documentation
4. Test with different JupiterEd configurations

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- Microsoft Graph API for To-Do integration
- Chrome Extensions API for platform support
- The JupiterEd community for inspiration

## 📞 Support

- **Documentation**: [SETUP.md](SETUP.md)
- **Issues**: [GitHub Issues](https://github.com/Pikachoo1111/JupiterEdMicrosoftToDo/issues)
- **Discussions**: [GitHub Discussions](https://github.com/Pikachoo1111/JupiterEdMicrosoftToDo/discussions)

---

Made with ❤️ for students who want to stay organized and never miss an assignment!
