(() => {
  const assignments = [];
  let currentCourse = "Unknown course";

  const nextWeekdayDate = (weekday) => {
    const today = new Date();
    const weekdays = ["Sun","Mon","<PERSON><PERSON>","Wed","<PERSON>hu","<PERSON><PERSON>","Sat"];
    const dayIndex = weekdays.indexOf(weekday);
    if (dayIndex === -1) return null;
    const date = new Date(today);
    const diff = (dayIndex + 7 - today.getDay()) % 7 || 7;
    date.setDate(today.getDate() + diff);
    return date;
  };

  const formatDate = (date) => {
    const mm = String(date.getMonth()+1).padStart(2,'0');
    const dd = String(date.getDate()).padStart(2,'0');
    const yyyy = date.getFullYear();
    return `${yyyy}-${mm}-${dd}`; // Microsoft To Do uses ISO format
  };

  const rows = document.querySelectorAll('tr.hi, tr.rowhi');

  rows.forEach(row => {
    if (row.classList.contains('hi')) {
      const courseName = row.querySelector('div.big.wrap')?.innerText.trim();
      if (courseName) {
        currentCourse = courseName.replace(/\d+(\.\d+)?%.*$/g, '').trim();
      }
    } else if (row.classList.contains('rowhi')) {
      const title = row.querySelector('td:nth-child(3) div.landonly.printbold')?.innerText.trim() || "No title";
      const dueRaw = row.querySelector('td:nth-child(2) div.printbold')?.innerText.trim() || "No due date";

      let dueDate = dueRaw.replace(/^Due\s+/i, '').trim();

      let normalizedDate;
      if (/^\d{1,2}\/\d{1,2}$/.test(dueDate)) {
        const [m,d] = dueDate.split('/').map(Number);
        const today = new Date();
        normalizedDate = formatDate(new Date(today.getFullYear(), m-1, d));
      } else {
        const nextDate = nextWeekdayDate(dueDate);
        normalizedDate = nextDate ? formatDate(nextDate) : null;
      }

      assignments.push({
        course: currentCourse,
        title: title,
        dueDate: normalizedDate
      });
    }
  });

  // Send to background for uploading
  chrome.runtime.sendMessage({ type: 'UPLOAD_ASSIGNMENTS', assignments });
})();
