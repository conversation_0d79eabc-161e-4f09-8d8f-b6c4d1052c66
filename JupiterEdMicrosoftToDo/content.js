// Content script for JupiterEd to Microsoft To-Do extension
class JupiterEdScraper {
  constructor() {
    this.assignments = [];
    this.currentCourse = "Unknown course";
    this.isJupiterEdPage = this.detectJupiterEdPage();

    if (this.isJupiterEdPage) {
      this.init();
    }
  }

  detectJupiterEdPage() {
    // Check if we're on a JupiterEd page
    const hostname = window.location.hostname;
    const isJupiterEd = hostname.includes('jupitered.com') ||
                       hostname.includes('schoology.com') ||
                       document.querySelector('tr.hi, tr.rowhi') !== null;
    if (isJupiterEd) {  
      console.log('JupiterEd page detected');
    }
    return isJupiterEd;
  }

  init() {
    // Add visual indicator that extension is active
    this.addExtensionIndicator();

    // Set up automatic scraping if enabled
    this.checkAutoSync();

    // Listen for manual trigger from popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'SCRAPE_ASSIGNMENTS') {
        this.scrapeAssignments()
          .then(assignments => sendResponse({ success: true, assignments }))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // Keep message channel open
      }
    });
  }

  addExtensionIndicator() {
    // Add a small indicator to show the extension is active
    const indicator = document.createElement('div');
    indicator.id = 'jupitered-todo-indicator';
    indicator.innerHTML = '📋 To-Do Sync Active';
    indicator.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: #0078d4;
      color: white;
      padding: 5px 10px;
      border-radius: 5px;
      font-size: 12px;
      z-index: 10000;
      font-family: Arial, sans-serif;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    `;
    document.body.appendChild(indicator);

    // Remove indicator after 3 seconds
    setTimeout(() => {
      if (indicator.parentNode) {
        indicator.parentNode.removeChild(indicator);
      }
    }, 3000);
  }

  async checkAutoSync() {
    try {
      const result = await chrome.storage.sync.get(['autoSync']);
      if (result.autoSync) {
        // Wait a bit for page to fully load
        setTimeout(() => {
          this.scrapeAndUpload();
        }, 2000);
      }
    } catch (error) {
      console.error('Error checking auto sync:', error);
    }
  }

  async scrapeAssignments() {
    this.assignments = [];
    this.currentCourse = "Unknown course";

    try {
      // Try multiple approaches for different JupiterEd layouts
      let assignments = [];

      // Method 1: Traditional table layout (tr.hi, tr.rowhi)
      const tableRows = document.querySelectorAll('tr.hi, tr.rowhi');
      if (tableRows.length > 0) {
        console.log('Using table row method, found', tableRows.length, 'rows');
        tableRows.forEach(row => this.processRow(row));
        assignments = [...this.assignments];
      }

      // Method 2: Text-based extraction for your format
      if (assignments.length === 0) {
        console.log('Trying text-based extraction...');
        assignments = this.extractFromText();
      }

      // Method 3: Generic assignment containers
      if (assignments.length === 0) {
        const containers = document.querySelectorAll('.assignment-row, [data-assignment], .assignment-item');
        console.log('Using container method, found', containers.length, 'containers');
        containers.forEach(container => this.processContainer(container));
        assignments = [...this.assignments];
      }

      if (assignments.length === 0) {
        throw new Error('No assignments found on this page. The page layout might not be supported yet.');
      }

      // Remove duplicates
      this.assignments = this.removeDuplicates(assignments);

      console.log(`Scraped ${this.assignments.length} assignments:`, this.assignments);
      return this.assignments;
    } catch (error) {
      console.error('Error scraping assignments:', error);
      throw error;
    }
  }

  processRow(row) {
    try {
      if (row.classList.contains('hi')) {
        // Course header row
        const courseName = row.querySelector('div.big.wrap')?.innerText.trim() ||
                          row.querySelector('.course-name')?.innerText.trim() ||
                          row.querySelector('td:first-child')?.innerText.trim();

        if (courseName) {
          this.currentCourse = courseName.replace(/\d+(\.\d+)?%.*$/g, '').trim();
        }
      } else if (row.classList.contains('rowhi') || row.dataset.assignment) {
        // Assignment row
        const assignment = this.extractAssignmentData(row);
        if (assignment && assignment.title && assignment.title !== "No title") {
          this.assignments.push(assignment);
        }
      }
    } catch (error) {
      console.warn('Error processing row:', error, row);
    }
  }

  extractAssignmentData(row) {
    // Try multiple selectors for title
    const title = row.querySelector('td:nth-child(3) div.landonly.printbold')?.innerText.trim() ||
                  row.querySelector('.assignment-title')?.innerText.trim() ||
                  row.querySelector('[data-title]')?.innerText.trim() ||
                  row.querySelector('td:nth-child(2)')?.innerText.trim() ||
                  "No title";

    // Try multiple selectors for due date
    const dueRaw = row.querySelector('td:nth-child(2) div.printbold')?.innerText.trim() ||
                   row.querySelector('.due-date')?.innerText.trim() ||
                   row.querySelector('[data-due]')?.innerText.trim() ||
                   row.querySelector('td:first-child')?.innerText.trim() ||
                   "No due date";

    // Try to extract description/details
    const description = row.querySelector('.assignment-description')?.innerText.trim() ||
                       row.querySelector('td:nth-child(4)')?.innerText.trim() ||
                       '';

    const dueDate = this.parseDueDate(dueRaw);

    return {
      course: this.currentCourse,
      title: title,
      dueDate: dueDate,
      description: description,
      source: 'JupiterEd',
      scrapedAt: new Date().toISOString()
    };
  }

  parseDueDate(dueRaw) {
    if (!dueRaw || dueRaw === "No due date") return null;

    // Clean up the due date string
    let dueDate = dueRaw.replace(/^Due\s+/i, '').trim();

    try {
      // Handle MM/DD format
      if (/^\d{1,2}\/\d{1,2}$/.test(dueDate)) {
        const [month, day] = dueDate.split('/').map(Number);
        const today = new Date();
        const year = today.getFullYear();
        const date = new Date(year, month - 1, day);

        // If the date is in the past, assume it's for next year
        if (date < today) {
          date.setFullYear(year + 1);
        }

        return this.formatDate(date);
      }

      // Handle MM/DD/YYYY format
      if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dueDate)) {
        const [month, day, year] = dueDate.split('/').map(Number);
        return this.formatDate(new Date(year, month - 1, day));
      }

      // Handle weekday names (Mon, Tue, etc.)
      const weekdayDate = this.nextWeekdayDate(dueDate);
      if (weekdayDate) {
        return this.formatDate(weekdayDate);
      }

      // Try to parse as a regular date string
      const parsedDate = new Date(dueDate);
      if (!isNaN(parsedDate.getTime())) {
        return this.formatDate(parsedDate);
      }

      return null;
    } catch (error) {
      console.warn('Error parsing due date:', dueDate, error);
      return null;
    }
  }

  nextWeekdayDate(weekday) {
    const today = new Date();
    const weekdays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const dayIndex = weekdays.findIndex(day =>
      weekday.toLowerCase().startsWith(day.toLowerCase())
    );

    if (dayIndex === -1) return null;

    const date = new Date(today);
    const diff = (dayIndex + 7 - today.getDay()) % 7 || 7;
    date.setDate(today.getDate() + diff);
    return date;
  }

  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  removeDuplicates(assignments) {
    const seen = new Set();
    return assignments.filter(assignment => {
      const key = `${assignment.course}:${assignment.title}:${assignment.dueDate}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  async scrapeAndUpload() {
    try {
      const assignments = await this.scrapeAssignments();

      if (assignments.length === 0) {
        console.log('No assignments found to upload');
        return;
      }

      // Send to background script for uploading
      chrome.runtime.sendMessage(
        { type: 'UPLOAD_ASSIGNMENTS', assignments },
        (response) => {
          if (chrome.runtime.lastError) {
            console.error('Error sending message:', chrome.runtime.lastError);
            return;
          }

          if (response?.error) {
            console.error('Upload error:', response.error);
            this.showNotification('Upload failed: ' + response.error, 'error');
          } else if (response) {
            console.log('Upload results:', response);
            this.showNotification(
              `Successfully uploaded ${response.success} assignments${response.failed > 0 ? `, ${response.failed} failed` : ''}`,
              response.failed > 0 ? 'warning' : 'success'
            );
          }
        }
      );
    } catch (error) {
      console.error('Error in scrapeAndUpload:', error);
      this.showNotification('Error scraping assignments: ' + error.message, 'error');
    }
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 50px;
      right: 10px;
      padding: 10px 15px;
      border-radius: 5px;
      color: white;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 10001;
      max-width: 300px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
      ${type === 'success' ? 'background: #28a745;' : ''}
      ${type === 'error' ? 'background: #dc3545;' : ''}
      ${type === 'warning' ? 'background: #ffc107; color: black;' : ''}
      ${type === 'info' ? 'background: #17a2b8;' : ''}
    `;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }
}

// Initialize the scraper
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => new JupiterEdScraper());
} else {
  new JupiterEdScraper();
}
