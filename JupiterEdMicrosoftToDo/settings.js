// Settings page script for JupiterEd to Microsoft To-Do extension
class SettingsController {
  constructor() {
    this.elements = {
      status: document.getElementById('status'),
      redirectUri: document.getElementById('redirectUri'),
      clientId: document.getElementById('clientId'),
      testAuthBtn: document.getElementById('testAuthBtn'),
      logoutBtn: document.getElementById('logoutBtn'),
      authStatus: document.getElementById('authStatus'),
      todoList: document.getElementById('todoList'),
      refreshListsBtn: document.getElementById('refreshListsBtn'),
      autoSync: document.getElementById('autoSync'),
      notifications: document.getElementById('notifications'),
      saveBtn: document.getElementById('saveBtn'),
      resetBtn: document.getElementById('resetBtn'),
      supportLink: document.getElementById('supportLink')
    };

    this.init();
  }

  async init() {
    // Display redirect URI for Azure setup
    this.elements.redirectUri.textContent = chrome.identity.getRedirectURL();

    // Set up event listeners
    this.setupEventListeners();

    // Load current settings
    await this.loadSettings();

    // Check authentication status
    await this.checkAuthStatus();
  }

  setupEventListeners() {
    this.elements.testAuthBtn.addEventListener('click', () => this.testAuthentication());
    this.elements.logoutBtn.addEventListener('click', () => this.logout());
    this.elements.refreshListsBtn.addEventListener('click', () => this.refreshTodoLists());
    this.elements.saveBtn.addEventListener('click', () => this.saveSettings());
    this.elements.resetBtn.addEventListener('click', () => this.resetSettings());
    this.elements.supportLink.addEventListener('click', (e) => {
      e.preventDefault();
      this.openSupport();
    });

    // Auto-save certain settings
    this.elements.clientId.addEventListener('blur', () => this.saveClientId());
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get([
        'clientId',
        'selectedListId',
        'autoSync',
        'notificationsEnabled'
      ]);

      this.elements.clientId.value = result.clientId || '';
      this.elements.autoSync.checked = result.autoSync || false;
      this.elements.notifications.checked = result.notificationsEnabled !== false;

      // Load todo lists if authenticated
      if (result.clientId) {
        await this.loadTodoLists(result.selectedListId);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      this.showStatus('Error loading settings: ' + error.message, 'error');
    }
  }

  async saveClientId() {
    const clientId = this.elements.clientId.value.trim();
    if (clientId) {
      try {
        await chrome.storage.sync.set({ clientId });
        this.showStatus('Client ID saved', 'success');
      } catch (error) {
        this.showStatus('Error saving Client ID: ' + error.message, 'error');
      }
    }
  }

  async saveSettings() {
    try {
      const settings = {
        clientId: this.elements.clientId.value.trim(),
        selectedListId: this.elements.todoList.value,
        autoSync: this.elements.autoSync.checked,
        notificationsEnabled: this.elements.notifications.checked
      };

      if (!settings.clientId) {
        this.showStatus('Please enter your Azure App Client ID', 'warning');
        return;
      }

      const response = await this.sendMessage({
        type: 'UPDATE_SETTINGS',
        settings
      });

      if (response.error) {
        throw new Error(response.error);
      }

      this.showStatus('Settings saved successfully!', 'success');
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showStatus('Error saving settings: ' + error.message, 'error');
    }
  }

  async resetSettings() {
    if (!confirm('Are you sure you want to reset all settings to defaults? This will also log you out.')) {
      return;
    }

    try {
      await chrome.storage.sync.clear();
      await chrome.storage.local.clear();
      
      // Reset form
      this.elements.clientId.value = '';
      this.elements.todoList.innerHTML = '<option value="">Loading lists...</option>';
      this.elements.autoSync.checked = false;
      this.elements.notifications.checked = true;

      this.showStatus('Settings reset to defaults', 'info');
      this.updateAuthStatus(false);
    } catch (error) {
      console.error('Error resetting settings:', error);
      this.showStatus('Error resetting settings: ' + error.message, 'error');
    }
  }

  async testAuthentication() {
    const clientId = this.elements.clientId.value.trim();
    if (!clientId) {
      this.showStatus('Please enter your Azure App Client ID first', 'warning');
      return;
    }

    // Save client ID first
    await this.saveClientId();

    this.setButtonLoading(this.elements.testAuthBtn, true);

    try {
      const response = await this.sendMessage({ type: 'AUTHENTICATE' });

      if (response.error) {
        throw new Error(response.error);
      }

      if (response.success) {
        this.showStatus('Authentication successful!', 'success');
        this.updateAuthStatus(true);
        await this.loadTodoLists();
      }
    } catch (error) {
      console.error('Authentication error:', error);
      this.showStatus('Authentication failed: ' + error.message, 'error');
      this.updateAuthStatus(false);
    } finally {
      this.setButtonLoading(this.elements.testAuthBtn, false);
    }
  }

  async logout() {
    try {
      const response = await this.sendMessage({ type: 'LOGOUT' });
      
      if (response.error) {
        throw new Error(response.error);
      }

      this.showStatus('Logged out successfully', 'info');
      this.updateAuthStatus(false);
      this.elements.todoList.innerHTML = '<option value="">Not authenticated</option>';
    } catch (error) {
      console.error('Logout error:', error);
      this.showStatus('Error during logout: ' + error.message, 'error');
    }
  }

  async checkAuthStatus() {
    try {
      const response = await this.sendMessage({ type: 'GET_AUTH_STATUS' });
      this.updateAuthStatus(response.authenticated && response.hasClientId);
    } catch (error) {
      console.error('Error checking auth status:', error);
      this.updateAuthStatus(false);
    }
  }

  updateAuthStatus(authenticated) {
    const statusDiv = this.elements.authStatus;
    statusDiv.classList.remove('hidden');

    if (authenticated) {
      statusDiv.className = 'status success';
      statusDiv.textContent = '✅ Successfully authenticated with Microsoft To-Do';
      this.elements.logoutBtn.disabled = false;
    } else {
      statusDiv.className = 'status warning';
      statusDiv.textContent = '⚠️ Not authenticated - Click "Test Authentication" to sign in';
      this.elements.logoutBtn.disabled = true;
    }
  }

  async loadTodoLists(selectedListId = null) {
    this.setButtonLoading(this.elements.refreshListsBtn, true);

    try {
      const response = await this.sendMessage({ type: 'GET_TODO_LISTS' });

      if (response.error) {
        throw new Error(response.error);
      }

      const select = this.elements.todoList;
      select.innerHTML = '';

      if (response.lists && response.lists.length > 0) {
        response.lists.forEach(list => {
          const option = document.createElement('option');
          option.value = list.id;
          option.textContent = list.displayName;
          if (list.id === selectedListId) {
            option.selected = true;
          }
          select.appendChild(option);
        });

        // Select default "Tasks" list if no selection
        if (!selectedListId) {
          const defaultList = response.lists.find(list => 
            list.wellknownListName === 'defaultList' || 
            list.displayName.toLowerCase() === 'tasks'
          );
          if (defaultList) {
            select.value = defaultList.id;
          }
        }
      } else {
        select.innerHTML = '<option value="">No lists found</option>';
      }
    } catch (error) {
      console.error('Error loading todo lists:', error);
      this.elements.todoList.innerHTML = '<option value="">Error loading lists</option>';
      this.showStatus('Error loading To-Do lists: ' + error.message, 'error');
    } finally {
      this.setButtonLoading(this.elements.refreshListsBtn, false);
    }
  }

  async refreshTodoLists() {
    await this.loadTodoLists(this.elements.todoList.value);
  }

  openSupport() {
    chrome.tabs.create({ 
      url: 'https://github.com/Pikachoo1111/JupiterEdMicrosoftToDo/issues' 
    });
  }

  showStatus(message, type = 'info') {
    const statusDiv = this.elements.status;
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.classList.remove('hidden');

    // Auto-hide success messages
    if (type === 'success') {
      setTimeout(() => {
        statusDiv.classList.add('hidden');
      }, 5000);
    }
  }

  setButtonLoading(button, loading) {
    if (loading) {
      button.disabled = true;
      button.innerHTML = '<span class="loading"></span>' + button.textContent;
    } else {
      button.disabled = false;
      button.innerHTML = button.textContent.replace(/^.*?(\w)/, '$1');
    }
  }

  async sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          resolve({ error: chrome.runtime.lastError.message });
        } else {
          resolve(response || {});
        }
      });
    });
  }
}

// Initialize settings when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new SettingsController();
});
