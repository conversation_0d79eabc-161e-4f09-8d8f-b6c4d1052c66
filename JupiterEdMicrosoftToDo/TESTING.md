# Testing Guide for JupiterEd to Microsoft To-Do Extension

This guide will help you test the extension functionality before using it with real JupiterEd data.

## Pre-Testing Setup

### 1. Install the Extension
1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select the `JupiterEdMicrosoftToDo` folder
5. The extension should appear in your extensions list

### 2. Verify Installation
- Extension icon should appear in Chrome toolbar
- Click the icon - popup should open
- Click "Settings" - settings page should open in new tab

## Testing Scenarios

### Test 1: Basic Functionality Test

1. **Open Test Page**
   - Navigate to `chrome-extension://[your-extension-id]/test.html`
   - Or open the `test.html` file in Chrome
   - You should see a simulated JupiterEd assignments page

2. **Test Extension Detection**
   - Click the extension icon
   - Status should show "Compatible page detected"
   - "Sync Assignments" button should be enabled

3. **Test Without Authentication**
   - Click "Sync Assignments"
   - Should show error about setup being required

### Test 2: Authentication Setup

1. **Configure Azure App**
   - Follow the SETUP.md guide to create Azure App Registration
   - Copy the Client ID

2. **Configure Extension**
   - Click extension icon → Settings
   - Paste your Client ID
   - Click "Test Authentication"
   - Complete Microsoft sign-in flow
   - Should show "Authentication successful!"

3. **Select To-Do List**
   - After authentication, lists should load automatically
   - Select your preferred list (or leave as default "Tasks")
   - Click "Save All Settings"

### Test 3: Assignment Sync Test

1. **Open Test Page**
   - Go back to the test.html page
   - Click the extension icon

2. **Perform Sync**
   - Click "Sync Assignments"
   - Should show loading spinner
   - Should complete with success message (e.g., "Successfully synced 12 assignments")

3. **Verify in Microsoft To-Do**
   - Open [Microsoft To-Do](https://to-do.office.com)
   - Check your selected list
   - Should see 12 new tasks with course prefixes:
     - "Mathematics: Chapter 8 Homework - Quadratic Equations"
     - "English Literature: Essay - Character Analysis of Hamlet"
     - etc.

### Test 4: Real JupiterEd Testing

1. **Navigate to JupiterEd**
   - Log into your actual JupiterEd account
   - Go to the assignments page

2. **Test Detection**
   - Click extension icon
   - Should detect the page as compatible
   - If not, the page layout might be different

3. **Test Real Sync**
   - Click "Sync Assignments"
   - Check results in Microsoft To-Do

## Troubleshooting Tests

### Common Issues to Test

1. **Authentication Failures**
   - Test with wrong Client ID
   - Test with missing permissions
   - Test token expiration (wait 1 hour and try again)

2. **Page Compatibility**
   - Test on non-JupiterEd pages (should show warning)
   - Test on different JupiterEd layouts
   - Test with no assignments visible

3. **Sync Issues**
   - Test with no internet connection
   - Test with invalid To-Do list selection
   - Test duplicate sync (should not create duplicates)

## Expected Test Results

### Test Page Results
The test.html page should generate these assignments:

**Mathematics - Advanced Algebra:**
- Chapter 8 Homework - Quadratic Equations (Due 12/15)
- Quiz Preparation - Polynomial Functions (Due Mon)
- Final Project - Real World Applications (Due 12/20)

**English Literature - AP English:**
- Essay - Character Analysis of Hamlet (Due Wed)
- Reading Assignment - Chapters 15-20 (Due 12/18)

**Chemistry - Honors Chemistry:**
- Lab Report - Acid-Base Titration (Due Fri)
- Problem Set - Stoichiometry (Due 12/22)
- Study Guide - Periodic Trends (Due Thu)

**World History - Modern World History:**
- Research Paper - Industrial Revolution (Due 12/16)
- Timeline Project - 20th Century Events (Due Tue)

**Physics - AP Physics 1:**
- Lab Practical - Momentum and Collisions (Due 12/19)
- Problem Set - Wave Properties (Due Mon)

### Date Parsing Tests
The extension should correctly parse:
- MM/DD format (12/15, 12/18, etc.) → YYYY-MM-DD
- Weekday names (Mon, Tue, Wed, Thu, Fri) → Next occurrence of that weekday

## Performance Testing

### Load Testing
1. Create a test page with 50+ assignments
2. Test sync performance
3. Verify all assignments are processed

### Error Recovery Testing
1. Disconnect internet during sync
2. Test with malformed assignment data
3. Test with missing due dates

## Security Testing

### Authentication Security
1. Verify tokens are stored securely
2. Test token refresh functionality
3. Verify logout clears all tokens

### Data Privacy
1. Verify no data is sent to external servers
2. Check that only necessary permissions are requested
3. Verify assignment data is not stored permanently

## Reporting Issues

If you encounter issues during testing:

1. **Check Browser Console**
   - Press F12 → Console tab
   - Look for error messages
   - Include these in your report

2. **Check Extension Console**
   - Go to `chrome://extensions/`
   - Click "Inspect views: background page"
   - Check for errors in the background script

3. **Create Issue Report**
   - Go to GitHub Issues
   - Include:
     - Chrome version
     - Extension version
     - Steps to reproduce
     - Error messages
     - Screenshots if helpful

## Success Criteria

The extension passes testing if:
- ✅ Installs without errors
- ✅ Detects JupiterEd pages correctly
- ✅ Authenticates with Microsoft successfully
- ✅ Scrapes assignments from test page (12 assignments)
- ✅ Creates tasks in Microsoft To-Do with correct titles
- ✅ Parses due dates correctly
- ✅ Handles errors gracefully
- ✅ Provides clear user feedback
- ✅ Settings are saved and loaded correctly
- ✅ Works with real JupiterEd pages

Once all tests pass, the extension is ready for production use!
