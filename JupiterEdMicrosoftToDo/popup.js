// Popup script for JupiterEd to Microsoft To-Do extension
class PopupController {
  constructor() {
    this.elements = {
      authStatus: document.getElementById('authStatus'),
      authText: document.getElementById('authText'),
      pageStatus: document.getElementById('pageStatus'),
      pageText: document.getElementById('pageText'),
      syncBtn: document.getElementById('syncBtn'),
      settingsBtn: document.getElementById('settingsBtn'),
      loading: document.getElementById('loading'),
      message: document.getElementById('message'),
      lastSync: document.getElementById('lastSync'),
      helpLink: document.getElementById('helpLink'),
      aboutLink: document.getElementById('aboutLink')
    };

    this.currentTab = null;
    this.init();
  }

  async init() {
    // Get current tab
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    this.currentTab = tabs[0];

    // Set up event listeners
    this.setupEventListeners();

    // Check initial status
    await this.checkStatus();
  }

  setupEventListeners() {
    this.elements.syncBtn.addEventListener('click', () => this.syncAssignments());
    this.elements.settingsBtn.addEventListener('click', () => this.openSettings());
    this.elements.helpLink.addEventListener('click', (e) => {
      e.preventDefault();
      this.openSetupGuide();
    });
    this.elements.aboutLink.addEventListener('click', (e) => {
      e.preventDefault();
      this.showAbout();
    });
  }

  async checkStatus() {
    try {
      // Check authentication status
      const authResponse = await this.sendMessage({ type: 'GET_AUTH_STATUS' });
      this.updateAuthStatus(authResponse);

      // Check if current page is compatible
      this.checkPageCompatibility();

      // Load last sync info
      await this.loadLastSyncInfo();

    } catch (error) {
      console.error('Error checking status:', error);
      this.showMessage('Error checking extension status', 'error');
    }
  }

  updateAuthStatus(response) {
    if (response.error) {
      this.elements.authStatus.className = 'status-icon error';
      this.elements.authText.textContent = 'Authentication error';
      return;
    }

    if (!response.hasClientId) {
      this.elements.authStatus.className = 'status-icon warning';
      this.elements.authText.textContent = 'Setup required - Click Settings';
      return;
    }

    if (response.authenticated) {
      this.elements.authStatus.className = 'status-icon success';
      this.elements.authText.textContent = 'Connected to Microsoft To-Do';
    } else {
      this.elements.authStatus.className = 'status-icon warning';
      this.elements.authText.textContent = 'Authentication needed';
    }
  }

  checkPageCompatibility() {
    const url = this.currentTab.url;
    const isCompatible = url.includes('jupitered.com') ||
                        url.includes('schoology.com') ||
                        url.includes('localhost'); // For testing

    if (isCompatible) {
      this.elements.pageStatus.className = 'status-icon success';
      this.elements.pageText.textContent = 'Compatible page detected';
      this.elements.syncBtn.disabled = false;
    } else {
      this.elements.pageStatus.className = 'status-icon warning';
      this.elements.pageText.textContent = 'Navigate to JupiterEd assignments page';
      this.elements.syncBtn.disabled = true;
    }
  }

  async loadLastSyncInfo() {
    try {
      const result = await chrome.storage.local.get(['lastSync', 'lastSyncCount']);
      if (result.lastSync) {
        const date = new Date(result.lastSync);
        const count = result.lastSyncCount || 0;
        this.elements.lastSync.textContent =
          `Last sync: ${date.toLocaleDateString()} at ${date.toLocaleTimeString()} (${count} assignments)`;
      }
    } catch (error) {
      console.error('Error loading last sync info:', error);
    }
  }

  async syncAssignments() {
    this.showLoading(true);
    this.hideMessage();

    try {
      // First, scrape assignments from the current page
      const scrapeResponse = await this.sendMessageToTab({ type: 'SCRAPE_ASSIGNMENTS' });

      if (!scrapeResponse.success) {
        throw new Error(scrapeResponse.error || 'Failed to scrape assignments');
      }

      const assignments = scrapeResponse.assignments;

      if (assignments.length === 0) {
        this.showMessage('No assignments found on this page', 'warning');
        return;
      }

      // Upload assignments to Microsoft To-Do
      const uploadResponse = await this.sendMessage({
        type: 'UPLOAD_ASSIGNMENTS',
        assignments
      });

      if (uploadResponse.error) {
        throw new Error(uploadResponse.error);
      }

      // Save sync info
      await chrome.storage.local.set({
        lastSync: new Date().toISOString(),
        lastSyncCount: uploadResponse.success
      });

      // Show success message
      const message = `Successfully synced ${uploadResponse.success} assignments` +
                     (uploadResponse.failed > 0 ? `, ${uploadResponse.failed} failed` : '');

      this.showMessage(message, uploadResponse.failed > 0 ? 'warning' : 'success');

      // Update last sync display
      await this.loadLastSyncInfo();

    } catch (error) {
      console.error('Sync error:', error);
      this.showMessage('Sync failed: ' + error.message, 'error');
    } finally {
      this.showLoading(false);
    }
  }

  openSettings() {
    chrome.tabs.create({ url: chrome.runtime.getURL('settings.html') });
    window.close();
  }

  openSetupGuide() {
    chrome.tabs.create({
      url: 'https://github.com/Pikachoo1111/JupiterEdMicrosoftToDo/blob/main/SETUP.md'
    });
  }

  showAbout() {
    const aboutText = `JupiterEd to Microsoft To-Do Extension v1.0.0

This extension helps you sync assignments from JupiterEd to Microsoft To-Do automatically.

Features:
• Automatic assignment detection
• Customizable sync settings
• Support for due dates and descriptions
• Secure Microsoft authentication

Created with ❤️ for students everywhere.`;

    alert(aboutText);
  }

  showLoading(show) {
    this.elements.loading.classList.toggle('show', show);
    this.elements.syncBtn.disabled = show;
  }

  showMessage(text, type = 'info') {
    this.elements.message.textContent = text;
    this.elements.message.className = `message ${type} show`;

    // Auto-hide success messages after 5 seconds
    if (type === 'success') {
      setTimeout(() => this.hideMessage(), 5000);
    }
  }

  hideMessage() {
    this.elements.message.classList.remove('show');
  }

  async sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          resolve({ error: chrome.runtime.lastError.message });
        } else {
          resolve(response || {});
        }
      });
    });
  }

  async sendMessageToTab(message) {
    return new Promise((resolve) => {
      chrome.tabs.sendMessage(this.currentTab.id, message, (response) => {
        if (chrome.runtime.lastError) {
          resolve({ success: false, error: chrome.runtime.lastError.message });
        } else {
          resolve(response || { success: false, error: 'No response' });
        }
      });
    });
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});
