let accessToken = null;

// Receive messages from content script
chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
  if (message.type === 'UPLOAD_ASSIGNMENTS') {
    const tasks = message.assignments;
    if (!accessToken) {
      await authenticate();
    }
    if (accessToken) {
      for (const task of tasks) {
        await createTask(task);
      }
      console.log("All tasks uploaded!");
    }
  }
});

// OAuth2 authentication
async function authenticate() {
  return new Promise((resolve, reject) => {
    chrome.identity.launchWebAuthFlow(
      {
        url: `https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id=YOUR_AZURE_APP_CLIENT_ID&response_type=token&redirect_uri=${encodeURIComponent(chrome.identity.getRedirectURL())}&scope=Tasks.ReadWrite`,
        interactive: true
      },
      function (redirect_url) {
        if (chrome.runtime.lastError) {
          console.error(chrome.runtime.lastError);
          return reject(chrome.runtime.lastError);
        }
        const params = new URLSearchParams(new URL(redirect_url).hash.substring(1));
        accessToken = params.get('access_token');
        resolve();
      }
    );
  });
}

// Upload a task
async function createTask(task) {
  const body = {
    title: `${task.course}: ${task.title}`,
    dueDateTime: task.dueDate ? { dateTime: task.dueDate + "T17:00:00", timeZone: "UTC" } : undefined
  };
  await fetch('https://graph.microsoft.com/v1.0/me/todo/lists/tasks/tasks', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(body)
  });
}
