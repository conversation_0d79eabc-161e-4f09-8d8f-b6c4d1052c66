// Background script for JupiterEd to Microsoft To-Do extension
class JupiterEdToDoExtension {
  constructor() {
    this.accessToken = null;
    this.tokenExpiry = null;
    this.clientId = null;
    this.selectedListId = null;
    this.settings = {};
    this.init();
  }

  async init() {
    // Load settings from storage
    await this.loadSettings();

    // Set up message listeners
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Set up installation handler
    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install') {
        chrome.tabs.create({ url: chrome.runtime.getURL('settings.html') });
      }
    });
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get([
        'clientId',
        'selectedListId',
        'autoSync',
        'notificationsEnabled',
        'accessToken',
        'tokenExpiry'
      ]);

      this.clientId = result.clientId;
      this.selectedListId = result.selectedListId || 'tasks'; // Default to main tasks list
      this.settings = {
        autoSync: result.autoSync || false,
        notificationsEnabled: result.notificationsEnabled !== false
      };

      // Load stored token if valid
      if (result.accessToken && result.tokenExpiry && new Date(result.tokenExpiry) > new Date()) {
        this.accessToken = result.accessToken;
        this.tokenExpiry = new Date(result.tokenExpiry);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }

  async saveSettings() {
    try {
      await chrome.storage.sync.set({
        clientId: this.clientId,
        selectedListId: this.selectedListId,
        autoSync: this.settings.autoSync,
        notificationsEnabled: this.settings.notificationsEnabled,
        accessToken: this.accessToken,
        tokenExpiry: this.tokenExpiry?.toISOString()
      });
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'UPLOAD_ASSIGNMENTS':
          await this.uploadAssignments(message.assignments, sendResponse);
          break;
        case 'GET_AUTH_STATUS':
          sendResponse({ authenticated: !!this.accessToken, hasClientId: !!this.clientId });
          break;
        case 'AUTHENTICATE':
          await this.authenticate(sendResponse);
          break;
        case 'GET_TODO_LISTS':
          await this.getTodoLists(sendResponse);
          break;
        case 'UPDATE_SETTINGS':
          await this.updateSettings(message.settings, sendResponse);
          break;
        case 'LOGOUT':
          await this.logout(sendResponse);
          break;
        default:
          sendResponse({ error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ error: error.message });
    }
  }

  async uploadAssignments(assignments, sendResponse) {
    if (!this.clientId) {
      sendResponse({ error: 'Client ID not configured. Please set up the extension first.' });
      return;
    }

    if (!this.accessToken || (this.tokenExpiry && new Date() >= this.tokenExpiry)) {
      const authResult = await this.authenticate();
      if (!authResult.success) {
        sendResponse({ error: 'Authentication failed: ' + authResult.error });
        return;
      }
    }

    const results = {
      success: 0,
      failed: 0,
      errors: []
    };

    for (const assignment of assignments) {
      try {
        await this.createTask(assignment);
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`${assignment.title}: ${error.message}`);
      }
    }

    if (this.settings.notificationsEnabled) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'JupiterEd Sync Complete',
        message: `${results.success} tasks created, ${results.failed} failed`
      });
    }

    sendResponse(results);
  }

  async authenticate(sendResponse = null) {
    if (!this.clientId) {
      const error = 'Client ID not configured';
      if (sendResponse) sendResponse({ success: false, error });
      return { success: false, error };
    }

    try {
      const result = await new Promise((resolve, reject) => {
        const redirectUri = chrome.identity.getRedirectURL();
        const authUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize?` +
          `client_id=${this.clientId}&` +
          `response_type=token&` +
          `redirect_uri=${encodeURIComponent(redirectUri)}&` +
          `scope=${encodeURIComponent('https://graph.microsoft.com/Tasks.ReadWrite offline_access')}&` +
          `response_mode=fragment`;

        chrome.identity.launchWebAuthFlow({
          url: authUrl,
          interactive: true
        }, (redirectUrl) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }

          if (!redirectUrl) {
            reject(new Error('Authentication cancelled'));
            return;
          }

          try {
            const url = new URL(redirectUrl);
            const params = new URLSearchParams(url.hash.substring(1));
            const accessToken = params.get('access_token');
            const expiresIn = params.get('expires_in');

            if (!accessToken) {
              reject(new Error('No access token received'));
              return;
            }

            resolve({ accessToken, expiresIn });
          } catch (error) {
            reject(error);
          }
        });
      });

      this.accessToken = result.accessToken;
      this.tokenExpiry = new Date(Date.now() + (parseInt(result.expiresIn) * 1000));
      await this.saveSettings();

      if (sendResponse) sendResponse({ success: true });
      return { success: true };
    } catch (error) {
      console.error('Authentication error:', error);
      if (sendResponse) sendResponse({ success: false, error: error.message });
      return { success: false, error: error.message };
    }
  }

  async getTodoLists(sendResponse) {
    if (!this.accessToken) {
      sendResponse({ error: 'Not authenticated' });
      return;
    }

    try {
      const response = await fetch('https://graph.microsoft.com/v1.0/me/todo/lists', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      sendResponse({ success: true, lists: data.value });
    } catch (error) {
      console.error('Error fetching todo lists:', error);
      sendResponse({ error: error.message });
    }
  }

  async updateSettings(settings, sendResponse) {
    try {
      if (settings.clientId !== undefined) this.clientId = settings.clientId;
      if (settings.selectedListId !== undefined) this.selectedListId = settings.selectedListId;
      if (settings.autoSync !== undefined) this.settings.autoSync = settings.autoSync;
      if (settings.notificationsEnabled !== undefined) this.settings.notificationsEnabled = settings.notificationsEnabled;

      await this.saveSettings();
      sendResponse({ success: true });
    } catch (error) {
      console.error('Error updating settings:', error);
      sendResponse({ error: error.message });
    }
  }

  async logout(sendResponse) {
    try {
      this.accessToken = null;
      this.tokenExpiry = null;
      await chrome.storage.sync.remove(['accessToken', 'tokenExpiry']);
      sendResponse({ success: true });
    } catch (error) {
      console.error('Error during logout:', error);
      sendResponse({ error: error.message });
    }
  }

  async createTask(assignment) {
    if (!this.accessToken) {
      throw new Error('Not authenticated');
    }

    const taskData = {
      title: `${assignment.course}: ${assignment.title}`,
      body: {
        content: assignment.description || '',
        contentType: 'text'
      }
    };

    // Add due date if available
    if (assignment.dueDate) {
      taskData.dueDateTime = {
        dateTime: assignment.dueDate + 'T17:00:00.000Z',
        timeZone: 'UTC'
      };
    }

    const listId = this.selectedListId || 'tasks';
    const response = await fetch(`https://graph.microsoft.com/v1.0/me/todo/lists/${listId}/tasks`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(taskData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Failed to create task: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`);
    }

    return await response.json();
  }
}

// Initialize the extension
const extension = new JupiterEdToDoExtension();
