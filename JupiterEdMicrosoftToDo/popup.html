<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      min-height: 400px;
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
    }

    .header {
      background: linear-gradient(135deg, #0078d4, #106ebe);
      color: white;
      padding: 20px;
      text-align: center;
    }

    .header h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .header p {
      margin: 5px 0 0 0;
      font-size: 12px;
      opacity: 0.9;
    }

    .content {
      padding: 20px;
      background: white;
    }

    .status-section {
      margin-bottom: 20px;
      padding: 15px;
      border-radius: 8px;
      border: 1px solid #e1e1e1;
    }

    .status-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 14px;
    }

    .status-item:last-child {
      margin-bottom: 0;
    }

    .status-icon {
      width: 16px;
      height: 16px;
      margin-right: 10px;
      border-radius: 50%;
    }

    .status-icon.success {
      background: #28a745;
    }

    .status-icon.error {
      background: #dc3545;
    }

    .status-icon.warning {
      background: #ffc107;
    }

    .btn {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      margin-bottom: 10px;
      transition: all 0.2s;
    }

    .btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .btn-primary {
      background: #0078d4;
      color: white;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-success {
      background: #28a745;
      color: white;
    }

    .btn-outline {
      background: transparent;
      border: 1px solid #0078d4;
      color: #0078d4;
    }

    .loading {
      display: none;
      text-align: center;
      padding: 20px;
    }

    .loading.show {
      display: block;
    }

    .spinner {
      border: 2px solid #f3f3f3;
      border-top: 2px solid #0078d4;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
      margin: 0 auto 10px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .message {
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      font-size: 13px;
      display: none;
    }

    .message.show {
      display: block;
    }

    .message.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .message.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .message.warning {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }

    .footer {
      padding: 15px 20px;
      background: #f8f9fa;
      border-top: 1px solid #e1e1e1;
      text-align: center;
    }

    .footer a {
      color: #0078d4;
      text-decoration: none;
      font-size: 12px;
    }

    .footer a:hover {
      text-decoration: underline;
    }

    .assignment-count {
      font-weight: 600;
      color: #0078d4;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>JupiterEd → To-Do</h1>
    <p>Sync your assignments to Microsoft To-Do</p>
  </div>

  <div class="content">
    <div id="message" class="message"></div>

    <div class="status-section">
      <div class="status-item">
        <div id="authStatus" class="status-icon"></div>
        <span id="authText">Checking authentication...</span>
      </div>
      <div class="status-item">
        <div id="pageStatus" class="status-icon"></div>
        <span id="pageText">Checking page compatibility...</span>
      </div>
    </div>

    <div id="loading" class="loading">
      <div class="spinner"></div>
      <p>Processing assignments...</p>
    </div>

    <div id="actions">
      <button id="syncBtn" class="btn btn-primary" disabled>
        Sync Assignments
      </button>

      <button id="settingsBtn" class="btn btn-outline">
        Settings
      </button>
    </div>

    <div id="lastSync" style="font-size: 12px; color: #666; margin-top: 15px; text-align: center;">
    </div>
  </div>

  <div class="footer">
    <a href="#" id="helpLink">Setup Guide</a> |
    <a href="#" id="aboutLink">About</a>
  </div>

  <script src="popup.js"></script>
</body>
</html>
