#!/usr/bin/env python3
"""
Simple script to create placeholder PNG icons for the Chrome extension
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL not available, creating simple colored squares instead")

def create_icon(size, filename):
    if PIL_AVAILABLE:
        # Create icon with PIL
        img = Image.new('RGBA', (size, size), (0, 120, 212, 255))  # Microsoft blue
        draw = ImageDraw.Draw(img)
        
        # Draw checklist lines
        line_color = (255, 255, 255, 255)  # White
        line_width = max(1, size // 16)
        
        # Three horizontal lines representing tasks
        y_positions = [size // 4, size // 2, 3 * size // 4]
        for y in y_positions:
            draw.rectangle([size // 4, y - line_width//2, 3 * size // 4, y + line_width//2], fill=line_color)
        
        # Draw checkmark on last line
        if size >= 32:
            check_size = size // 8
            check_x = size - size // 4
            check_y = y_positions[-1]
            draw.ellipse([check_x - check_size, check_y - check_size, 
                         check_x + check_size, check_y + check_size], fill=(40, 167, 69, 255))  # Green
            
            # Simple checkmark
            draw.line([check_x - check_size//2, check_y, check_x - check_size//4, check_y + check_size//2], 
                     fill=(255, 255, 255, 255), width=max(1, size // 32))
            draw.line([check_x - check_size//4, check_y + check_size//2, check_x + check_size//2, check_y - check_size//2], 
                     fill=(255, 255, 255, 255), width=max(1, size // 32))
        
        img.save(filename, 'PNG')
    else:
        # Fallback: create simple colored square
        img = Image.new('RGB', (size, size), (0, 120, 212))
        img.save(filename, 'PNG')

def main():
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        filename = f'icon{size}.png'
        create_icon(size, filename)
        print(f'Created {filename}')

if __name__ == '__main__':
    main()
