# JupiterEd to Microsoft To-Do Extension Setup Guide

This guide will help you set up the JupiterEd to Microsoft To-Do Chrome extension to automatically sync your assignments.

## Prerequisites

- Google Chrome browser
- Microsoft account (personal or work/school)
- Access to Azure Portal (free with any Microsoft account)
- JupiterEd account and access to assignments page

## Step 1: Install the Extension

1. Download or clone this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the `JupiterEdMicrosoftToDo` folder
5. The extension should now appear in your extensions list

## Step 2: Create Azure App Registration

### 2.1 Access Azure Portal
1. Go to [Azure Portal](https://portal.azure.com)
2. Sign in with your Microsoft account
3. If you don't have an Azure subscription, you can use the free tier

### 2.2 Create App Registration
1. In the Azure Portal, search for "Azure Active Directory" or "App registrations"
2. Click on "App registrations" in the left sidebar
3. Click "New registration"

### 2.3 Configure App Registration
Fill in the registration form:

**Name:** `JupiterEd To-Do Sync` (or any name you prefer)

**Supported account types:** Select "Accounts in any organizational directory and personal Microsoft accounts (personal Microsoft accounts, e.g. Skype, Xbox)"

**Redirect URI:** 
- Platform: Web
- URI: Copy this from the extension settings page (it will look like `https://[extension-id].chromiumapp.org/`)

### 2.4 Get Client ID
1. After creating the app, you'll be taken to the app overview page
2. Copy the "Application (client) ID" - you'll need this for the extension

### 2.5 Configure API Permissions
1. In your app registration, click "API permissions" in the left sidebar
2. Click "Add a permission"
3. Select "Microsoft Graph"
4. Choose "Delegated permissions"
5. Search for and select "Tasks.ReadWrite"
6. Click "Add permissions"
7. If you're an admin, click "Grant admin consent for [your organization]"

## Step 3: Configure the Extension

1. Click the extension icon in Chrome
2. Click "Settings" to open the settings page
3. Paste your Azure App Client ID in the "Azure App Client ID" field
4. Click "Test Authentication" to sign in to Microsoft
5. Select your preferred To-Do list from the dropdown
6. Configure your sync preferences:
   - Enable automatic sync if you want assignments synced automatically
   - Enable notifications to see sync status

## Step 4: Using the Extension

### Automatic Sync (if enabled)
- Navigate to your JupiterEd assignments page
- The extension will automatically detect and sync new assignments
- You'll see a brief notification confirming the sync

### Manual Sync
- Navigate to your JupiterEd assignments page
- Click the extension icon
- Click "Sync Assignments"
- Wait for the sync to complete

## Troubleshooting

### Common Issues

**"Authentication failed"**
- Verify your Client ID is correct
- Make sure the redirect URI in Azure matches exactly
- Check that Tasks.ReadWrite permission is granted

**"No assignments found"**
- Make sure you're on the JupiterEd assignments page
- The page should show a table with assignment rows
- Try refreshing the page and syncing again

**"Failed to create task"**
- Check your internet connection
- Verify you have write access to the selected To-Do list
- Try re-authenticating in the settings

**Extension not working on JupiterEd**
- Make sure you're on the correct assignments page
- Some JupiterEd instances may have different layouts
- Check the browser console for error messages

### Getting Help

If you encounter issues:

1. Check the [GitHub Issues](https://github.com/Pikachoo1111/JupiterEdMicrosoftToDo/issues) page
2. Create a new issue with:
   - Your Chrome version
   - JupiterEd URL (without personal info)
   - Error messages from the browser console
   - Steps to reproduce the problem

## Privacy and Security

- Your Azure Client ID is stored locally in Chrome
- Authentication tokens are handled securely by Chrome's identity API
- No assignment data is stored permanently - it's only used during sync
- All communication with Microsoft Graph API uses HTTPS

## Advanced Configuration

### Custom To-Do Lists
- You can create custom lists in Microsoft To-Do
- The extension will detect and allow you to select any list
- Assignments will be created in your selected list

### Date Handling
- The extension attempts to parse various date formats from JupiterEd
- Due dates are set to 5:00 PM UTC by default
- If a date can't be parsed, the task is created without a due date

### Duplicate Prevention
- The extension checks for duplicate assignments based on course, title, and due date
- Existing tasks won't be duplicated if you sync multiple times

## Uninstalling

To remove the extension:
1. Go to `chrome://extensions/`
2. Find the JupiterEd to Microsoft To-Do extension
3. Click "Remove"
4. Optionally, revoke permissions in your Azure App registration

Your Microsoft To-Do tasks will remain - only the extension is removed.
