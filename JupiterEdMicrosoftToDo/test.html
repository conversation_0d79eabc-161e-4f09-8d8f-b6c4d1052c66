<!DOCTYPE html>
<html>
<head>
    <title>JupiterEd Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0078d4;
            text-align: center;
            margin-bottom: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        tr.hi {
            background: #e7f3ff;
            font-weight: bold;
        }
        tr.rowhi {
            background: #f9f9f9;
            border-bottom: 1px solid #ddd;
        }
        tr.rowhi:hover {
            background: #f0f8ff;
        }
        td {
            padding: 12px;
            border: 1px solid #ddd;
            vertical-align: top;
        }
        .big.wrap {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .landonly.printbold {
            font-weight: bold;
            color: #0066cc;
        }
        .printbold {
            font-weight: bold;
            color: #d9534f;
        }
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin: 0 0 10px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JupiterEd Test Page</h1>
        
        <div class="instructions">
            <h3>🧪 Extension Testing Instructions</h3>
            <p>This page simulates a JupiterEd assignments page for testing the Chrome extension:</p>
            <ol>
                <li>Make sure the extension is installed and configured</li>
                <li>Click the extension icon in Chrome</li>
                <li>Click "Sync Assignments" to test the scraping functionality</li>
                <li>Check your Microsoft To-Do for the synced assignments</li>
            </ol>
        </div>

        <table>
            <!-- Course 1: Mathematics -->
            <tr class="hi">
                <td colspan="4">
                    <div class="big wrap">Mathematics - Advanced Algebra 85.5% A</div>
                </td>
            </tr>
            <tr class="rowhi">
                <td>1</td>
                <td><div class="printbold">Due 12/15</div></td>
                <td><div class="landonly printbold">Chapter 8 Homework - Quadratic Equations</div></td>
                <td>Complete problems 1-25 on page 156. Show all work and graph solutions.</td>
            </tr>
            <tr class="rowhi">
                <td>2</td>
                <td><div class="printbold">Due Mon</div></td>
                <td><div class="landonly printbold">Quiz Preparation - Polynomial Functions</div></td>
                <td>Review chapters 6-7 and complete practice quiz online.</td>
            </tr>
            <tr class="rowhi">
                <td>3</td>
                <td><div class="printbold">Due 12/20</div></td>
                <td><div class="landonly printbold">Final Project - Real World Applications</div></td>
                <td>Create a presentation showing how algebra is used in your chosen career field.</td>
            </tr>

            <!-- Course 2: English Literature -->
            <tr class="hi">
                <td colspan="4">
                    <div class="big wrap">English Literature - AP English 92.3% A</div>
                </td>
            </tr>
            <tr class="rowhi">
                <td>1</td>
                <td><div class="printbold">Due Wed</div></td>
                <td><div class="landonly printbold">Essay - Character Analysis of Hamlet</div></td>
                <td>Write a 5-page essay analyzing Hamlet's character development throughout the play.</td>
            </tr>
            <tr class="rowhi">
                <td>2</td>
                <td><div class="printbold">Due 12/18</div></td>
                <td><div class="landonly printbold">Reading Assignment - Chapters 15-20</div></td>
                <td>Read assigned chapters and complete comprehension questions.</td>
            </tr>

            <!-- Course 3: Chemistry -->
            <tr class="hi">
                <td colspan="4">
                    <div class="big wrap">Chemistry - Honors Chemistry 78.9% B+</div>
                </td>
            </tr>
            <tr class="rowhi">
                <td>1</td>
                <td><div class="printbold">Due Fri</div></td>
                <td><div class="landonly printbold">Lab Report - Acid-Base Titration</div></td>
                <td>Complete lab report including data analysis, calculations, and conclusions.</td>
            </tr>
            <tr class="rowhi">
                <td>2</td>
                <td><div class="printbold">Due 12/22</div></td>
                <td><div class="landonly printbold">Problem Set - Stoichiometry</div></td>
                <td>Solve problems 1-30 from chapter 12. Show balanced equations and calculations.</td>
            </tr>
            <tr class="rowhi">
                <td>3</td>
                <td><div class="printbold">Due Thu</div></td>
                <td><div class="landonly printbold">Study Guide - Periodic Trends</div></td>
                <td>Complete study guide for upcoming test on periodic table trends.</td>
            </tr>

            <!-- Course 4: History -->
            <tr class="hi">
                <td colspan="4">
                    <div class="big wrap">World History - Modern World History 88.7% A-</div>
                </td>
            </tr>
            <tr class="rowhi">
                <td>1</td>
                <td><div class="printbold">Due 12/16</div></td>
                <td><div class="landonly printbold">Research Paper - Industrial Revolution</div></td>
                <td>Write a 7-page research paper on the social impacts of the Industrial Revolution.</td>
            </tr>
            <tr class="rowhi">
                <td>2</td>
                <td><div class="printbold">Due Tue</div></td>
                <td><div class="landonly printbold">Timeline Project - 20th Century Events</div></td>
                <td>Create an interactive timeline of major 20th century historical events.</td>
            </tr>

            <!-- Course 5: Physics -->
            <tr class="hi">
                <td colspan="4">
                    <div class="big wrap">Physics - AP Physics 1 91.2% A</div>
                </td>
            </tr>
            <tr class="rowhi">
                <td>1</td>
                <td><div class="printbold">Due 12/19</div></td>
                <td><div class="landonly printbold">Lab Practical - Momentum and Collisions</div></td>
                <td>Demonstrate understanding of conservation of momentum through practical experiments.</td>
            </tr>
            <tr class="rowhi">
                <td>2</td>
                <td><div class="printbold">Due Mon</div></td>
                <td><div class="landonly printbold">Problem Set - Wave Properties</div></td>
                <td>Complete wave mechanics problems focusing on frequency, wavelength, and amplitude.</td>
            </tr>
        </table>

        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 4px;">
            <h3>Expected Test Results:</h3>
            <ul>
                <li><strong>Total Assignments:</strong> 12 assignments across 5 courses</li>
                <li><strong>Date Formats:</strong> Mix of MM/DD dates and weekday names (Mon, Tue, Wed, Thu, Fri)</li>
                <li><strong>Courses:</strong> Mathematics, English Literature, Chemistry, World History, Physics</li>
                <li><strong>Due Dates:</strong> Various dates in December plus next weekdays</li>
            </ul>
            <p><em>After syncing, check your Microsoft To-Do list for these assignments with proper course prefixes and due dates.</em></p>
        </div>
    </div>
</body>
</html>
