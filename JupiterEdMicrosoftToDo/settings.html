<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>JupiterEd to Microsoft To-Do - Settings</title>
  <style>
    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 0;
      background: #f5f5f5;
      line-height: 1.6;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      min-height: 100vh;
    }

    .header {
      background: linear-gradient(135deg, #0078d4, #106ebe);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header p {
      margin: 10px 0 0 0;
      font-size: 16px;
      opacity: 0.9;
    }

    .content {
      padding: 30px;
    }

    .section {
      margin-bottom: 40px;
      padding: 25px;
      border: 1px solid #e1e1e1;
      border-radius: 8px;
      background: #fafafa;
    }

    .section h2 {
      margin: 0 0 20px 0;
      font-size: 20px;
      color: #333;
      border-bottom: 2px solid #0078d4;
      padding-bottom: 10px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #555;
    }

    .form-group input[type="text"],
    .form-group select {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }

    .form-group input[type="text"]:focus,
    .form-group select:focus {
      outline: none;
      border-color: #0078d4;
      box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
    }

    .form-group input[type="checkbox"] {
      margin-right: 8px;
    }

    .help-text {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      margin-right: 10px;
      margin-bottom: 10px;
      transition: all 0.2s;
    }

    .btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .btn-primary {
      background: #0078d4;
      color: white;
    }

    .btn-success {
      background: #28a745;
      color: white;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-danger {
      background: #dc3545;
      color: white;
    }

    .status {
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
      font-size: 14px;
    }

    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .status.warning {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }

    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    .setup-steps {
      background: #e7f3ff;
      border: 1px solid #b3d9ff;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
    }

    .setup-steps h3 {
      margin: 0 0 15px 0;
      color: #0066cc;
    }

    .setup-steps ol {
      margin: 0;
      padding-left: 20px;
    }

    .setup-steps li {
      margin-bottom: 10px;
    }

    .setup-steps code {
      background: #f1f1f1;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }

    .hidden {
      display: none;
    }

    .loading {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #0078d4;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .footer {
      text-align: center;
      padding: 20px;
      color: #666;
      font-size: 14px;
      border-top: 1px solid #e1e1e1;
    }

    .footer a {
      color: #0078d4;
      text-decoration: none;
    }

    .footer a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Extension Settings</h1>
      <p>Configure your JupiterEd to Microsoft To-Do integration</p>
    </div>

    <div class="content">
      <div id="status" class="status hidden"></div>

      <!-- Setup Instructions -->
      <div class="section">
        <h2>🚀 Setup Instructions</h2>
        <div class="setup-steps">
          <h3>Before you begin:</h3>
          <ol>
            <li>Go to <a href="https://portal.azure.com" target="_blank">Azure Portal</a> and sign in</li>
            <li>Navigate to "Azure Active Directory" → "App registrations"</li>
            <li>Click "New registration" and create an app with these settings:
              <ul>
                <li>Name: "JupiterEd To-Do Sync" (or any name you prefer)</li>
                <li>Supported account types: "Accounts in any organizational directory and personal Microsoft accounts"</li>
                <li>Redirect URI: <code id="redirectUri">Loading...</code></li>
              </ul>
            </li>
            <li>After creating the app, copy the "Application (client) ID" and paste it below</li>
            <li>Go to "API permissions" → "Add a permission" → "Microsoft Graph" → "Delegated permissions"</li>
            <li>Add the "Tasks.ReadWrite" permission</li>
            <li>Click "Grant admin consent" if you're an admin, or ask your admin to approve</li>
          </ol>
        </div>
      </div>

      <!-- Authentication Settings -->
      <div class="section">
        <h2>🔐 Authentication</h2>
        <div class="form-group">
          <label for="clientId">Azure App Client ID:</label>
          <input type="text" id="clientId" placeholder="Enter your Azure App Client ID">
          <div class="help-text">This is the Application (client) ID from your Azure App registration</div>
        </div>
        
        <div class="form-group">
          <button id="testAuthBtn" class="btn btn-primary">Test Authentication</button>
          <button id="logoutBtn" class="btn btn-secondary">Logout</button>
        </div>
        
        <div id="authStatus" class="hidden"></div>
      </div>

      <!-- Microsoft To-Do Settings -->
      <div class="section">
        <h2>📋 Microsoft To-Do Settings</h2>
        <div class="form-group">
          <label for="todoList">Target To-Do List:</label>
          <select id="todoList">
            <option value="">Loading lists...</option>
          </select>
          <div class="help-text">Choose which Microsoft To-Do list to add assignments to</div>
        </div>
        
        <button id="refreshListsBtn" class="btn btn-secondary">Refresh Lists</button>
      </div>

      <!-- Sync Settings -->
      <div class="section">
        <h2>⚙️ Sync Settings</h2>
        <div class="form-group">
          <label>
            <input type="checkbox" id="autoSync">
            Enable automatic sync when visiting JupiterEd pages
          </label>
          <div class="help-text">When enabled, assignments will be synced automatically when you visit compatible pages</div>
        </div>
        
        <div class="form-group">
          <label>
            <input type="checkbox" id="notifications">
            Show sync notifications
          </label>
          <div class="help-text">Display notifications when assignments are successfully synced</div>
        </div>
      </div>

      <!-- Actions -->
      <div class="section">
        <h2>💾 Save Settings</h2>
        <button id="saveBtn" class="btn btn-success">Save All Settings</button>
        <button id="resetBtn" class="btn btn-danger">Reset to Defaults</button>
      </div>
    </div>

    <div class="footer">
      <p>
        <a href="https://github.com/Pikachoo1111/JupiterEdMicrosoftToDo" target="_blank">GitHub Repository</a> |
        <a href="#" id="supportLink">Support</a> |
        Version 1.0.0
      </p>
    </div>
  </div>

  <script src="settings.js"></script>
</body>
</html>
